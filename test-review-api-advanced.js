const axios = require('axios');

// Configuration
const BASE_URL = 'https://apis.dentalkart.com/review/api/v1';
const ORDER_ID = '091920422-00-523091';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTAyMzg2LCJ0eXBlIjoiY3VzdG9tZXIiLCJpYXQiOjE3NTE1MzE2NTEsImlzcyI6ImRlbnRhbGthcnQifQ.CFi5pLMCQDBE3fyOtfdepHBRqBU7eK_PXkorTzxKaSk';

// Common headers
const commonHeaders = {
  'Accept': 'application/json',
  'Authorization': AUTH_TOKEN,
  'app_version': '13.0.2',
  'device_id': '99E4C10E-B686-4559-BFD2-3F033ECB04F7',
  'platform': 'ios',
  'user-agent': 'ios',
  'version': '13.0.2',
  'x-api-key': 'ZFobrRyccnTyXyXHPUVO4eyyKEKoSjWB',
  'Content-Type': 'application/json'
};

// Test scenarios
const testScenarios = [
  {
    name: 'Original Test - Rating 3',
    data: {
      "items": [
        {
          "id": 3286842,
          "product_id": 49855,
          "source": "ios",
          "media": [],
          "title": "Testing purpose",
          "rating": 3
        }
      ],
      "order_rating": 3
    }
  },
  {
    name: 'High Rating Test - Rating 5',
    data: {
      "items": [
        {
          "id": 3286842,
          "product_id": 49855,
          "source": "ios",
          "media": [],
          "title": "Excellent product!",
          "rating": 5
        }
      ],
      "order_rating": 5
    }
  },
  {
    name: 'Low Rating Test - Rating 1',
    data: {
      "items": [
        {
          "id": 3286842,
          "product_id": 49855,
          "source": "ios",
          "media": [],
          "title": "Not satisfied",
          "rating": 1
        }
      ],
      "order_rating": 1
    }
  },
  {
    name: 'Multiple Items Test',
    data: {
      "items": [
        {
          "id": 3286842,
          "product_id": 49855,
          "source": "ios",
          "media": [],
          "title": "First product review",
          "rating": 4
        },
        {
          "id": 3286843,
          "product_id": 49856,
          "source": "ios",
          "media": [],
          "title": "Second product review",
          "rating": 5
        }
      ],
      "order_rating": 4
    }
  }
];

// Function to run a single test
async function runTest(scenario, index) {
  console.log(`\n${'='.repeat(50)}`);
  console.log(`🧪 Test ${index + 1}: ${scenario.name}`);
  console.log(`${'='.repeat(50)}`);
  
  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: `${BASE_URL}/orders/${ORDER_ID}/rate`,
    headers: commonHeaders,
    data: JSON.stringify(scenario.data)
  };

  console.log('📝 Request Data:', JSON.stringify(scenario.data, null, 2));
  console.log('\n⏳ Sending request...');

  try {
    const response = await axios.request(config);
    console.log('✅ Success!');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    console.log('❌ Error!');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', JSON.stringify(error.response.data, null, 2));
      return { success: false, status: error.response.status, error: error.response.data };
    } else {
      console.log('Error:', error.message);
      return { success: false, error: error.message };
    }
  }
}

// Function to run all tests
async function runAllTests() {
  console.log('🚀 Starting Review API Tests...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📋 Order ID: ${ORDER_ID}`);
  console.log(`🔑 Auth Token: ${AUTH_TOKEN.substring(0, 20)}...`);
  
  const results = [];
  
  for (let i = 0; i < testScenarios.length; i++) {
    const result = await runTest(testScenarios[i], i);
    results.push({ scenario: testScenarios[i].name, ...result });
    
    // Wait 1 second between tests to avoid rate limiting
    if (i < testScenarios.length - 1) {
      console.log('\n⏱️  Waiting 1 second before next test...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // Summary
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 TEST SUMMARY');
  console.log(`${'='.repeat(60)}`);
  
  results.forEach((result, index) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${index + 1}. ${result.scenario}: ${status}`);
    if (!result.success) {
      console.log(`   Error: ${result.error || 'Unknown error'}`);
    }
  });
  
  const passCount = results.filter(r => r.success).length;
  console.log(`\n🎯 Results: ${passCount}/${results.length} tests passed`);
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, runTest, testScenarios };
