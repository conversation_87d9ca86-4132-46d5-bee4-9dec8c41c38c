interface CartData {
  is_cod_eligible: boolean;
  cart_amount: number;
  cart_weight: number;
}

export interface DeliveryRequestPayload {
  country_code: string;
  postcode: number;
  product_ids: number[];
  cart_data: CartData;
}

interface DeliveryDay {
  days: number;
  dispatch_days: number;
  message: string;
  product_id: number;
}

interface DeliveryInfo {
  delivery_days: DeliveryDay[];
  max_delivery_days: number;
  max_delivery_days_text: string;
  max_dispatch_days: number;
}

interface ServiceAvailability {
  errors: string[];
  message: string;
  serviceAvailable: boolean;
  services: {
    COD: boolean;
    Prepaid: boolean;
  };
}

export interface DeliveryResponse {
  delivery_info: DeliveryInfo[];
  service_availability: ServiceAvailability[];
}
