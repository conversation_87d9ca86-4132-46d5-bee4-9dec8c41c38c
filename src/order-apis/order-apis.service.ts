import {
  BadRequestException,
  Inject,
  UnauthorizedException,
  Req,
  InternalServerErrorException,
  Injectable,
} from '@nestjs/common';
import { SalesOrder } from '../database/entities/sales-order';
import { SalesOrderAddress } from '../database/entities/sales-order-address';
import { SalesOrderItem } from '../database/entities/sales-order-item';
import { SalesOrderPayment } from '../database/entities/sales-order-payment';
import { SalesOrderTax } from '../database/entities/sales-order-tax';
import { OrderMapper } from '../mapper/order';
import { OrderHelper } from '../utils/order.helper';
import { Sequelize } from 'sequelize-typescript';
import { SalesOrderTaxItem } from '../database/entities/sales-order-tax-item';
import { PaymentMethods, OrderStatuses } from '../config/constants';
import { find } from 'lodash';
import { NotifyERP } from '../utils/notify-erp';
import { RazorpayService } from '../razorpay/razorpay.service';
import { SalesOrderExtraInfo } from '../database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from '../database/entities/order-item-extra-info';
import { SalesOrderAmountPromotion } from '../database/entities/sales-order-amount-promotion';
import { CreateOrderDto } from '../dto/create-order.dto';
import { FetchOrderDto } from '../dto/fetch-payment.dto';
import { CodOrderPayload } from '../dto/cod-order-status.dto';
import { AvailablePaymentMethods } from 'src/interface/cart-external-response';
import { Request } from 'express';
import { SalesOrderItemPromotion } from 'src/database/entities/sales-order-item-promotion';

@Injectable()
export class OrderApisService {
  constructor(
    private readonly orderHelper: OrderHelper,
    private readonly orderMapper: OrderMapper,
    private readonly notifyERP: NotifyERP,
    private readonly razorpayService: RazorpayService,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  async createOrder(createOrderDto: CreateOrderDto, @Req() req: Request) {
    const { cart_id, payment_method, custom_attributes, is_buy_now } =
      createOrderDto;

    const authToken = this.orderHelper.getUserToken(
      req.headers['authorization'],
    );

    const platform = this.orderHelper.getHeaderValue(req.headers['platform']);
    const version = this.orderHelper.getHeaderValue(req.headers['version']);

    const cartAndCartPayment = await this.orderHelper.fetchCartPaymentInfo(
      cart_id,
      authToken ?? null,
      is_buy_now,
    );

    const { cart_info: cartExists, payment_info: cartPayment } =
      cartAndCartPayment;

    if (!cartExists || !cartExists.items) {
      return new BadRequestException('Invalid cartId');
    }

    if (cartExists && cartExists.customer.id && !authToken) {
      throw new UnauthorizedException('Unauthorized user');
    }

    if (
      cartPayment.payment_methods.map((o) => o.code).indexOf(payment_method) ===
      -1
    ) {
      return new BadRequestException('Invalid payment method');
    }

    const skus = this.orderHelper.extractAllSkus(cartExists);

    const products = await this.orderHelper.getProductDataFromSku(
      skus,
      cartExists?.extension_attributes?.shipping_assignments?.[0]?.shipping
        ?.address?.country_id || cartExists?.billing_address?.country_id,
    );

    const responseData = {
      amount: this.orderHelper.roundAndToBaseCurrencyAmount(
        Number(
          find(cartPayment.totals.total_segments, { code: 'grand_total' })
            ?.value,
        ) || 0,
        2,
        100,
      ),
      currency: cartExists.currency.quote_currency_code,
      merchant_id: '',
      order_number: '',
      payment_method: payment_method,
      reference_number: '',
    };

    const orderObj = await this.orderMapper.buildSalesOrderObj(
      cartExists,
      cartPayment,
      '',
      payment_method,
      platform,
      version,
    );

    const t1 = await this.sequelize.transaction();

    try {
      const salesOrderObj = await SalesOrder.create(orderObj, {
        transaction: t1,
      });

      const { billing_address, shipping_address } =
        this.orderMapper.buildSalesOrderAddressObj(
          cartExists,
          salesOrderObj.order_id,
        );

      //validate billing shipping address
      this.orderHelper.validateOrderAdddress(billing_address, cart_id);
      if (shipping_address) {
        this.orderHelper.validateOrderAdddress(shipping_address, cart_id);
      }

      const salesOrderAddresses = await Promise.all([
        await SalesOrderAddress.create(billing_address, {
          transaction: t1,
        }),
        shipping_address &&
          (await SalesOrderAddress.create(shipping_address, {
            transaction: t1,
          })),
      ]);

      const orderTaxObj = this.orderMapper.buildSalesOrderTaxObj(
        cartPayment,
        salesOrderObj.order_id,
      );
      const salesTaxObj = await SalesOrderTax.create(orderTaxObj, {
        transaction: t1,
      });

      // Build sales order items with tax and extra info
      const salesOrderItemsObj = this.orderMapper.buildSalesOrderItemObj(
        cartExists,
        cartPayment,
        salesOrderObj.order_id,
        products,
        salesTaxObj.tax_id,
      );

      // Get EDD info using product IDs from salesOrderItemsObj
      const eddInfo = await this.orderHelper.getEDDInfo(
        salesOrderObj,
        salesOrderItemsObj, // Use salesOrderItemsObj to get product IDs
        salesOrderAddresses,
        products?.productForService,
      );

      // Rebuild sales order items with delivery information included
      const salesOrderItemsObjWithDelivery =
        this.orderMapper.buildSalesOrderItemObj(
          cartExists,
          cartPayment,
          salesOrderObj.order_id,
          products,
          salesTaxObj.tax_id,
          eddInfo?.delivery_days, // Pass delivery days to include in order items
        );

      // Create sales order items with associated models
      const salesOrderItems = await SalesOrderItem.bulkCreate(
        salesOrderItemsObjWithDelivery,
        {
          include: [
            { model: SalesOrderTaxItem, as: 'tax' },
            { model: OrderItemExtraInfo, as: 'itemExtraInfo' },
            { model: SalesOrderItemPromotion, as: 'salesOrderItemPromotions' },
          ],
          transaction: t1,
        },
      );

      // Map and create cart amount promotions if they exist
      const amountPromotions = this.orderMapper.mapAmountPromotions(
        cartExists,
        salesOrderObj.order_id,
        products,
      );

      if (amountPromotions.length > 0) {
        salesOrderObj.salesOrderAmountPromotions =
          await SalesOrderAmountPromotion.bulkCreate(amountPromotions, {
            transaction: t1,
          });
      }

      let razorpayOrder;
      if (payment_method === PaymentMethods.RAZOR_PAY) {
        razorpayOrder = await this.razorpayService.createOrder({
          amount: this.orderHelper.roundAndToBaseCurrencyAmount(
            Number(salesOrderObj.grand_total) || 0,
            2,
            100,
          ),
          currency: salesOrderObj.order_currency_code,
          paymentId: salesOrderObj.order_id,
          orderId: salesOrderObj.increment_id,
        });
        responseData.merchant_id = razorpayOrder.merchantId;
        responseData.reference_number = razorpayOrder.referenceNumber;
      }
      const orderPaymentObj = this.orderMapper.buildSalesOrderPaymentObj(
        cartPayment,
        salesOrderObj.order_id,
        payment_method,
        razorpayOrder,
      );
      const salesOrderPayment = await SalesOrderPayment.create(
        orderPaymentObj,
        { transaction: t1 },
      );

      // build and save sales_order_extra_info values
      let sales_order_extra_info = null;

      if (eddInfo || custom_attributes) {
        const sales_order_extra_info_obj =
          this.orderMapper.buildSalesExtraInfoObject(
            salesOrderObj.order_id,
            custom_attributes,
            eddInfo,
          );
        sales_order_extra_info = await SalesOrderExtraInfo.create(
          sales_order_extra_info_obj,
          { transaction: t1 },
        );
      }

      await this.orderHelper.inActiveCart(cart_id, is_buy_now);

      await t1.commit();

      responseData.order_number = salesOrderObj.increment_id;

      const isFirstOrder = await this.orderHelper.isFirstOrder(
        salesOrderObj.customer_id,
      );

      // Notify ERP about order creation
      this.notifyERP.notifyStatusUpdate(
        salesOrderObj,
        salesOrderItems,
        salesOrderAddresses,
        salesOrderPayment,
        null,
        sales_order_extra_info,
        true,
        isFirstOrder,
      );

      //Update coupon usgae api call
      this.orderHelper.updateRuleUsage(salesOrderObj);
    } catch (error) {
      await t1.rollback();
      this.orderHelper.handleServiceError(
        error,
        `Error placing order for cart ID ${cart_id}`,
      );
    }

    return responseData;
  }

  async fetchOrderPaymentsDetails(
    fetchOrderDto: FetchOrderDto,
    @Req() req: Request,
  ) {
    const { order_id, rzp_order_id, rzp_payment_id } = fetchOrderDto;

    const authToken = this.orderHelper.getUserToken(
      req.headers['authorization'],
    );

    if (!authToken) {
      const order = await SalesOrder.findOne({
        where: {
          increment_id: order_id,
          customer_is_guest: true,
        },
        include: [
          {
            model: SalesOrderPayment,
          },
          {
            model: SalesOrderItem,
            attributes: ['product_id'],
          },
          {
            model: SalesOrderAddress,
          },
        ],
      });

      if (this.checkIsOrderExist(order, rzp_order_id, rzp_payment_id)) {
        throw new BadRequestException('Order not found');
      }
      const availablePaymentMethods = await this.getOrderPaymentMethods(order);
      const data = this.orderMapper.buildFetchOrderResponse(order);
      data['guest_user_email'] = order.customer_email;
      data['payment_methods'] = availablePaymentMethods;
      return data;
    }

    const customer = await this.orderHelper.getCustomerDetails(authToken);
    if (!customer) throw new UnauthorizedException('Unauthorized user');

    const order = await SalesOrder.findOne({
      where: {
        increment_id: order_id,
        customer_id: +customer.customer_id,
      },
      include: [
        {
          model: SalesOrderPayment,
        },
        {
          model: SalesOrderItem,
          attributes: ['product_id'],
        },
        {
          model: SalesOrderAddress,
        },
      ],
    });

    if (this.checkIsOrderExist(order, rzp_order_id, rzp_payment_id)) {
      throw new BadRequestException('Order not found');
    }
    const availablePaymentMethods = await this.getOrderPaymentMethods(order);
    const data = this.orderMapper.buildFetchOrderResponse(order);
    data['payment_methods'] = availablePaymentMethods;
    return data;
  }

  checkIsOrderExist(
    order: SalesOrder,
    rzp_order_id?: string,
    rzp_payment_id?: string,
  ) {
    return (
      !order ||
      (order?.payment?.method === PaymentMethods.RAZOR_PAY &&
        ((rzp_order_id && order?.payment?.razorpay_order_id !== rzp_order_id) ||
          (rzp_payment_id &&
            order?.payment?.razorpay_payment_id !== rzp_payment_id)))
    );
  }

  async placeCodOrder(updateOrderDto: CodOrderPayload, @Req() req: Request) {
    try {
      const { order_id, payment_method_code, guest_user_email } =
        updateOrderDto;

      if (payment_method_code !== PaymentMethods.COD)
        throw new BadRequestException('Invalid Payment methods code');

      const authToken = this.orderHelper.getUserToken(
        req.headers['authorization'],
      );

      if (!authToken) {
        if (!guest_user_email)
          throw new BadRequestException('Guest user email id is necessary');
        const res = await this.placeGuestCodOrder(
          order_id,
          guest_user_email,
          payment_method_code,
        );
        return res;
      }
      const customer = await this.orderHelper.getCustomerDetails(authToken);
      if (!customer) throw new UnauthorizedException('Unauthorized user');
      const res = await this.placeUserCodOrder(
        order_id,
        +customer.customer_id,
        payment_method_code,
      );
      return res;
    } catch (e) {
      if (e instanceof BadRequestException || UnauthorizedException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e?.message);
      }
    }
  }

  async findOrderByOption(options: {
    [key: string]: string | number | boolean;
  }) {
    const order = await SalesOrder.findOne({
      where: {
        ...options,
      },
      include: [
        SalesOrderAddress,
        {
          model: SalesOrderItem,
          include: [
            {
              model: OrderItemExtraInfo,
              attributes: ['referral_code', 'is_free_product'],
            },
            SalesOrderItemPromotion,
          ],
        },
        SalesOrderPayment,
        SalesOrderAmountPromotion,
        {
          model: SalesOrderExtraInfo,
          attributes: [
            'exp_delivery_days',
            'exp_dispatch_days',
            'extra_info_entity_id',
          ],
        },
      ],
    });
    return order;
  }

  async updateStatusAndNotifyErp(
    order: SalesOrder,
    orderId: number,
    status: string,
    payment_method_code: PaymentMethods.COD,
    orderExtraInfo: SalesOrderExtraInfo,
  ) {
    try {
      const paymentId = order.payment.payment_id;
      await Promise.all([
        SalesOrder.update({ status: status }, { where: { order_id: orderId } }),
        SalesOrderPayment.update(
          { method: payment_method_code },
          { where: { payment_id: paymentId } },
        ),
      ]);
      order.status = status;
      order.payment.method = payment_method_code;
      this.notifyERP.notifyStatusUpdate(
        order,
        order.items,
        order.address,
        order.payment,
        null,
        orderExtraInfo,
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  async placeGuestCodOrder(
    order_id: string,
    guest_user_email: string,
    payment_method_code: PaymentMethods.COD,
  ) {
    const order = await this.findOrderByOption({
      increment_id: order_id,
      customer_is_guest: true,
      customer_email: guest_user_email,
      status: OrderStatuses.PAYMENT_PENDING,
    });

    if (!order) throw new BadRequestException(`Order not found.`);
    return this.getCodOrderResponse(order, payment_method_code);
  }

  async placeUserCodOrder(
    order_id: string,
    customer_id: number,
    payment_method_code: PaymentMethods.COD,
  ) {
    const order = await this.findOrderByOption({
      increment_id: order_id,
      customer_id: customer_id,
      status: OrderStatuses.PAYMENT_PENDING,
    });
    if (!order) throw new BadRequestException(`Order not found`);
    return this.getCodOrderResponse(order, payment_method_code);
  }

  async getCodOrderResponse(
    order: SalesOrder,
    payment_method_code: PaymentMethods.COD,
  ) {
    const { productForService, availablePaymentMethods } =
      await this.orderHelper.getOrderAvailablePaymentMethods(order);

    const isExist = availablePaymentMethods?.find(
      (o: AvailablePaymentMethods) => o.code === payment_method_code,
    );

    if (!isExist)
      throw new BadRequestException(
        'Cash on delivery does not exist on this order.',
      );

    // Fetch EDD information
    const { max_delivery_days, max_dispatch_days } =
      (await this.orderHelper.getEDDInfo(
        order,
        order.items,
        order.address,
        productForService,
      )) || {};

    const orderExtraInfo = await this.orderHelper.addOrUpdateOrderExtraInfo(
      new Date(),
      order.order_id,
      max_delivery_days,
      max_dispatch_days,
      order.sales_order_extra_info,
    );

    const updatedData = await this.updateStatusAndNotifyErp(
      order,
      order.order_id,
      OrderStatuses.NEW_ORDER,
      payment_method_code,
      orderExtraInfo || order.sales_order_extra_info,
    );

    if (updatedData) {
      return this.orderMapper.buildOrderStatusChangeResponse(
        order.increment_id,
        OrderStatuses.NEW_ORDER,
      );
    }

    return this.orderMapper.buildOrderStatusChangeResponse(
      order.increment_id,
      order.status,
    );
  }

  async getOrderPaymentMethods(order: SalesOrder) {
    if (order.status === OrderStatuses.PAYMENT_PENDING) {
      const { availablePaymentMethods } =
        await this.orderHelper.getOrderAvailablePaymentMethods(order);
      return availablePaymentMethods;
    }
    return [];
  }

  async getRazorpayMethods() {
    const data = await this.orderHelper.getMethods();
    return { data };
  }
}
