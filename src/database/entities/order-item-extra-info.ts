import {
  Table,
  Model,
  Column,
  Foreign<PERSON>ey,
  BelongsTo,
  DataType,
} from 'sequelize-typescript';
import { SalesOrderItem } from './sales-order-item';
import { DECIMAL } from 'sequelize';

@Table({ tableName: 'order_item_extra_info', timestamps: true })
export class OrderItemExtraInfo extends Model {
  @Column({ type: DataType.INTEGER, autoIncrement: true, primaryKey: true })
  id: number;

  @Column({ type: DataType.INTEGER, allowNull: false })
  @ForeignKey(() => SalesOrderItem)
  item_id: number;

  @Column({ type: DataType.STRING })
  referral_code: string;

  @Column({ type: DataType.BOOLEAN })
  is_free_product: boolean;

  @Column({ type: DECIMAL(20, 2) })
  buying_guide_qty: number;

  @BelongsTo(() => SalesOrderItem)
  salesOrderItem: SalesOrderItem;
}
