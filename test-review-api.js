const axios = require('axios');

// Test data for order rating API
let data = JSON.stringify({
  "items": [
    {
      "id": 3286842,
      "product_id": 49855,
      "source": "ios",
      "media": [],
      "title": "Testing purpose",
      "rating": 3
    }
  ],
  "order_rating": 3
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://apis.dentalkart.com/review/api/v1/orders/091920422-00-523091/rate',
  headers: { 
    'Accept': 'application/json', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTAyMzg2LCJ0eXBlIjoiY3VzdG9tZXIiLCJpYXQiOjE3NTE1MzE2NTEsImlzcyI6ImRlbnRhbGthcnQifQ.CFi5pLMCQDBE3fyOtfdepHBRqBU7eK_PXkorTzxKaSk', 
    'app_version': '13.0.2', 
    'device_id': '99E4C10E-B686-4559-BFD2-3F033ECB04F7', 
    'platform': 'ios', 
    'user-agent': 'ios', 
    'version': '13.0.2', 
    'x-api-key': 'ZFobrRyccnTyXyXHPUVO4eyyKEKoSjWB', 
    'Content-Type': 'application/json'
  },
  data: data
};

console.log('🚀 Testing Review API...');
console.log('📝 Request Details:');
console.log('URL:', config.url);
console.log('Method:', config.method);
console.log('Data:', JSON.parse(data));
console.log('\n⏳ Sending request...\n');

axios.request(config)
  .then((response) => {
    console.log('✅ Success! Response received:');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
  })
  .catch((error) => {
    console.log('❌ Error occurred:');
    if (error.response) {
      // Server responded with error status
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Error Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      // Request was made but no response received
      console.log('No response received:', error.request);
    } else {
      // Something else happened
      console.log('Error:', error.message);
    }
  });
